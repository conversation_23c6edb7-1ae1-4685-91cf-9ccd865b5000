# كتيب هيكل مشروع VisionLens App

## مقدمة
هذا الكتيب يشرح هيكل مشروع VisionLens App بشكل مفصل، مع شرح وظيفة كل مجلد وملف، محتوياته، علاقته بفايربيس، وملاحظات أو اقتراحات تطويرية لكل جزء.

---

## 1. هيكل المشروع (Project Structure)

```
visionlensapp/
│
├── android/           # ملفات نظام أندرويد (تجميع، موارد، إعدادات)
│   └── app/
│       └── src/
│           └── main/
│               ├── java/      # كود جافا (Flutter plugins)
│               ├── kotlin/    # كود كوتلن (MainActivity)
│               └── res/       # موارد أندرويد (صور، قيم)
│
├── ios/               # ملفات نظام iOS (AppDelegate, Info.plist, موارد)
│   └── Runner/
│       └── Assets.xcassets/   # أيقونات وصور الإطلاق
│
├── web/               # ملفات الويب (index.html, manifest, icons)
│
├── linux/ macos/ windows/     # دعم منصات سطح المكتب (إعدادات افتراضية)
│
├── assets/            # موارد التطبيق (images, icons, fonts)
│
├── lib/               # كود التطبيق الأساسي
│   ├── models/        # النماذج (Product, User, Order...)
│   ├── screens/       # الشاشات (Home, Cart, Profile...)
│   ├── services/      # الخدمات (Firebase, Auth, Storage...)
│   ├── api_service.dart / api_service_mock.dart  # خدمات API ومحاكاة
│   ├── app_properties.dart    # الألوان والثيمات
│   ├── main.dart             # نقطة البداية
│   └── firebase_options.dart # إعدادات فايربيس
│
├── visionlens_app/    # نسخة إعدادات Firebase وسكريبتات مساعدة
│
├── ملفات إعدادات الجذر (yaml, md, bat, json, iml)
```

---

## 2. شرح كل جزء من الهيكل

### android/
- **الوظيفة:** كل ما يخص بناء التطبيق على أندرويد (كود، موارد، إعدادات، ربط فايربيس عبر google-services.json).
- **علاقته بفايربيس:** إعدادات الربط مع فايربيس تتم هنا.
- **ملاحظات:** تأكد من تحديث إعدادات الربط عند تغيير إعدادات فايربيس.

### ios/
- **الوظيفة:** ملفات بناء التطبيق على iOS (AppDelegate.swift، Info.plist، موارد الصور).
- **علاقته بفايربيس:** Info.plist يجب أن يحتوي إعدادات الربط مع فايربيس وGoogle Sign-In.
- **ملاحظات:** راجع إعدادات Info.plist عند أي تحديث.

### web/
- **الوظيفة:** ملفات تشغيل التطبيق على الويب (index.html، manifest.json، أيقونات).
- **علاقته بفايربيس:** index.html يجب أن يحتوي meta خاص بـ Google Sign-In Client ID.
- **ملاحظات:** تحقق من إعدادات Google Sign-In عند النشر.

### linux/ macos/ windows/
- **الوظيفة:** دعم منصات سطح المكتب (إعدادات افتراضية من Flutter).
- **ملاحظات:** غالباً لا تحتاج تعديل إلا إذا أردت دعم ميزات خاصة.

### assets/
- **الوظيفة:** موارد التطبيق: صور، أيقونات، خطوط.
- **ملاحظات:** منظمة جيداً، يفضل إضافة README داخل كل مجلد لشرح محتواه.

### lib/
#### models/
- **الوظيفة:** جميع النماذج (Product, User, Order, ...). تمثل البيانات التي يتعامل معها التطبيق وتستخدم في الخدمات والشاشات.
- **علاقته بفايربيس:** تُستخدم عند جلب/تخزين البيانات في Firestore.
- **ملاحظات:** جيدة، تدعم التحويل من/إلى JSON.

#### screens/
- **الوظيفة:** جميع الشاشات (Home, Cart, Profile, ...). تمثل واجهات المستخدم.
- **ملاحظات:** منظمة حسب الوظيفة، يفضل توثيق كل شاشة.

#### services/
- **الوظيفة:** جميع الخدمات (Firebase, Auth, Storage, ...). تدير الاتصال بفايربيس، المصادقة، التخزين، البحث، إلخ.
- **علاقته بفايربيس:** أساسي، يدير كل عمليات Firestore والمصادقة.
- **ملاحظات:** يوجد تكرار في بعض الخدمات (خاصة المصادقة)، يفضل توحيد الواجهة.

#### api_service.dart / api_service_mock.dart
- **الوظيفة:** خدمات API ومحاكاة، تستخدم عند الحاجة للربط مع API خارجي أو للاختبار.
- **علاقته بفايربيس:** إذا اعتمدت كلياً على فايرستور يمكن الاستغناء عنها.

#### app_properties.dart
- **الوظيفة:** الألوان والثيمات والثوابت.
- **ملاحظات:** جيد، يفضل دعم الوضع الليلي.

#### main.dart
- **الوظيفة:** نقطة البداية، تهيئة Firebase، إدارة الأخطاء، بدء التطبيق.
- **علاقته بفايربيس:** يهيئ Firebase وFirestore عبر خدمات منفصلة.
- **ملاحظات:** يفضل توحيد منطق التهيئة بين المنصات.

#### firebase_options.dart
- **الوظيفة:** إعدادات الاتصال بمشاريع فايربيس (مولد تلقائياً).
- **علاقته بفايربيس:** ضروري لتهيئة Firebase.
- **ملاحظات:** لا يعدل يدوياً.

### visionlens_app/
- **الوظيفة:** نسخة من إعدادات Firebase وسكريبتات مساعدة (لبيئة تطوير أو نشر منفصلة).
- **ملاحظات:** تأكد من مزامنة الإعدادات مع الجذر عند التحديث.

### ملفات إعدادات الجذر
- **الوظيفة:** ملفات yaml (إعدادات التحليل والحزم)، md (توثيق)، bat (سكريبتات)، json (إعدادات فايربيس)، iml (إعدادات IntelliJ/Android Studio).
- **ملاحظات:** منظمة جيداً، يفضل تحديث README ليشمل بنية المشروع.

---

## 3. ملاحظات عامة وإصلاحات مقترحة
- توحيد واجهات الخدمات (خاصة المصادقة وFirebase).
- إضافة اختبارات وحدات وتكامل.
- تحسين README ليشمل بنية المشروع.
- تحسين إدارة الأخطاء وتجربة المستخدم.
- دعم تعدد اللغات (i18n).
- تشفير البيانات الحساسة في التخزين المحلي.
- استخدام Provider أو Bloc لإدارة الحالة بشكل احترافي.
- مراجعة صلاحيات Firestore وStorage باستمرار.

---

## 4. الشاشات (Screens)

### نظرة عامة
جميع الشاشات موجودة في `lib/screens/`، وهي تمثل واجهات المستخدم الرئيسية والفرعية. كل شاشة غالباً عبارة عن StatefulWidget أو StatelessWidget، وتستخدم الخدمات لجلب البيانات أو تنفيذ العمليات.

### أمثلة على الشاشات الرئيسية:

#### 1. MainPage
- **الوظيفة:** شريط تنقل سفلي بين الصفحات الرئيسية (الرئيسية، الفئات، البحث، السلة، الملف الشخصي).
- **محتواها:** قائمة صفحات، BottomNavigationBar.
- **الاتصال بفايربيس:** غير مباشر، كل صفحة فرعية تتصل بالخدمات حسب الحاجة.

#### 2. HomePage
- **الوظيفة:** عرض المنتجات المميزة والجديدة والفئات.
- **محتواها:** سلايدر منتجات، قائمة فئات، منتجات جديدة.
- **الاتصال بفايربيس:** عبر FirestoreDataService لجلب المنتجات والفئات من Firestore.

#### 3. CartPage
- **الوظيفة:** إدارة السلة، عرض المنتجات المختارة، الانتقال للدفع.
- **محتواها:** قائمة المنتجات في السلة، أزرار تعديل الكمية، زر الدفع.
- **الاتصال بفايربيس:** تحميل المنتجات من التخزين المحلي، ويمكن إرسال الطلبات إلى Firestore عند الدفع.

#### 4. LoginPage
- **الوظيفة:** تسجيل الدخول بالبريد أو Google.
- **محتواها:** نموذج إدخال البريد وكلمة المرور، زر Google Sign-In.
- **الاتصال بفايربيس:** عبر خدمات المصادقة (AuthService) التي تتصل بـ Firebase Auth أو المحاكاة.

#### 5. ProfilePage
- **الوظيفة:** عرض بيانات المستخدم، الوصول لإعدادات الحساب (تعديل الملف، الإشعارات، الطلبات، إلخ).
- **محتواها:** بيانات المستخدم، روابط للشاشات الفرعية.
- **الاتصال بفايربيس:** جلب بيانات المستخدم من Firestore أو التخزين المحلي.

#### 6. SplashPage
- **الوظيفة:** شاشة البداية، تحديد حالة المستخدم والتوجيه المناسب.
- **محتواها:** أنيميشن، منطق التوجيه.
- **الاتصال بفايربيس:** التحقق من حالة المستخدم عبر AppState/SharedPreferences.

#### 7. شاشات فرعية أخرى
- **filters_page.dart:** فلترة المنتجات.
- **checkout_page.dart:** إتمام عملية الشراء.
- **order_history_page.dart:** عرض سجل الطلبات.
- **notifications_page.dart:** عرض الإشعارات.
- **search_page.dart:** البحث عن المنتجات.
- **edit_profile_page.dart:** تعديل بيانات المستخدم.
- **wishlist_page.dart:** عرض المفضلة.
- **payment_methods_page.dart:** إدارة طرق الدفع.

### ملاحظات عامة على الشاشات
- كل شاشة تعتمد على الخدمات (services) لجلب البيانات أو تنفيذ العمليات.
- الاتصال بفايربيس يتم غالباً عبر FirestoreDataService أو AuthService.
- يفضل توثيق كل شاشة ووظيفتها، وفصل منطق البيانات عن الواجهة.
- يمكن تحسين تجربة المستخدم بإضافة مؤشرات تحميل، رسائل خطأ واضحة، ودعم تعدد اللغات.

---

## خاتمة
هذا الكتيب يغطي هيكل المشروع بشكل مفصل، ويمكنك الرجوع إليه لفهم البنية أو عند تطوير أو إصلاح أي جزء. إذا احتجت شرحاً أعمق لأي ملف أو جزء، يمكنك طلب ذلك في أي وقت.
