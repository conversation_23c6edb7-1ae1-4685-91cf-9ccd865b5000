import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../../app_properties.dart';
import '../../models/product.dart';
import '../../models/order.dart';
import '../../services/firebase_web_service.dart';
import '../../api_service_mock.dart';

class DirectPurchasePage extends StatefulWidget {
  final Product product;
  final String? selectedColor;
  final String? selectedSize;
  final int quantity;

  const DirectPurchasePage({
    super.key,
    required this.product,
    this.selectedColor,
    this.selectedSize,
    this.quantity = 1,
  });

  @override
  State<DirectPurchasePage> createState() => _DirectPurchasePageState();
}

class _DirectPurchasePageState extends State<DirectPurchasePage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _addressController = TextEditingController();
  final _notesController = TextEditingController();

  bool _isLoading = false;
  String _paymentMethod = 'cash_on_delivery';

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  double get _totalPrice {
    return widget.product.finalPrice * widget.quantity;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إتمام الطلب'),
        backgroundColor: AppColors.primaryColor,
        foregroundColor: AppColors.white,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppDimensions.paddingMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // معلومات المنتج
              _buildProductInfo(),

              const SizedBox(height: 24),

              // معلومات العميل
              _buildCustomerInfo(),

              const SizedBox(height: 24),

              // طريقة الدفع
              _buildPaymentMethod(),

              const SizedBox(height: 24),

              // ملخص الطلب
              _buildOrderSummary(),

              const SizedBox(height: 32),

              // زر إتمام الطلب
              _buildSubmitButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProductInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // صورة المنتج
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: AppColors.lightGrey,
              borderRadius: BorderRadius.circular(
                AppDimensions.borderRadiusMedium,
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(
                AppDimensions.borderRadiusMedium,
              ),
              child: widget.product.image.isNotEmpty
                  ? Image.network(
                      widget.product.image,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Icon(
                          Icons.visibility,
                          size: 30,
                          color: AppColors.grey.withValues(alpha: 0.5),
                        );
                      },
                    )
                  : Icon(
                      Icons.visibility,
                      size: 30,
                      color: AppColors.grey.withValues(alpha: 0.5),
                    ),
            ),
          ),

          const SizedBox(width: 16),

          // معلومات المنتج
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.product.name,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  widget.product.brand ?? widget.product.categoryName,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.secondaryText,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Text(
                      AppConstants.formatPrice(widget.product.finalPrice),
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppColors.primaryColor,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      'الكمية: ${widget.quantity}',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                if (widget.selectedColor != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    'اللون: ${widget.selectedColor}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.secondaryText,
                    ),
                  ),
                ],
                if (widget.selectedSize != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    'المقاس: ${widget.selectedSize}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.secondaryText,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCustomerInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'معلومات العميل',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          // الاسم
          TextFormField(
            controller: _nameController,
            decoration: const InputDecoration(
              labelText: 'الاسم الكامل *',
              prefixIcon: Icon(Icons.person),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'يرجى إدخال الاسم الكامل';
              }
              return null;
            },
          ),

          const SizedBox(height: 16),

          // رقم الهاتف
          TextFormField(
            controller: _phoneController,
            decoration: const InputDecoration(
              labelText: 'رقم الهاتف *',
              prefixIcon: Icon(Icons.phone),
            ),
            keyboardType: TextInputType.phone,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'يرجى إدخال رقم الهاتف';
              }
              return null;
            },
          ),

          const SizedBox(height: 16),

          // العنوان
          TextFormField(
            controller: _addressController,
            decoration: const InputDecoration(
              labelText: 'العنوان *',
              prefixIcon: Icon(Icons.location_on),
            ),
            maxLines: 3,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'يرجى إدخال العنوان';
              }
              return null;
            },
          ),

          const SizedBox(height: 16),

          // ملاحظات
          TextFormField(
            controller: _notesController,
            decoration: const InputDecoration(
              labelText: 'ملاحظات إضافية (اختياري)',
              prefixIcon: Icon(Icons.note),
            ),
            maxLines: 2,
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentMethod() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'طريقة الدفع',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          RadioListTile<String>(
            title: const Text('الدفع عند الاستلام'),
            subtitle: const Text('ادفع نقداً عند وصول الطلب'),
            value: 'cash_on_delivery',
            groupValue: _paymentMethod,
            onChanged: (value) {
              setState(() {
                _paymentMethod = value!;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildOrderSummary() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'ملخص الطلب',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('سعر المنتج:'),
              Text(AppConstants.formatPrice(widget.product.finalPrice)),
            ],
          ),

          const SizedBox(height: 8),

          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('الكمية: ${widget.quantity}'),
              Text(AppConstants.formatPrice(_totalPrice)),
            ],
          ),

          const Divider(height: 24),

          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'المجموع الكلي:',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
              ),
              Text(
                AppConstants.formatPrice(_totalPrice),
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.primaryColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _submitOrder,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primaryColor,
          padding: const EdgeInsets.symmetric(vertical: 16),
        ),
        child: _isLoading
            ? const CircularProgressIndicator(color: AppColors.white)
            : const Text(
                'تأكيد الطلب',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
      ),
    );
  }

  Future<void> _submitOrder() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // إنشاء طلب جديد
      final order = Order(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        date: DateTime.now(),
        items: [
          OrderItem(
            productId: widget.product.id,
            productName: widget.product.name,
            imageUrl: widget.product.image,
            price: widget.product.finalPrice,
            quantity: widget.quantity,
            selectedColor: widget.selectedColor,
            selectedSize: widget.selectedSize,
          ),
        ],
        total: _totalPrice,
        status: OrderStatus.processing,
        shippingAddress:
            '${_nameController.text.trim()}\n${_phoneController.text.trim()}\n${_addressController.text.trim()}',
        notes: _notesController.text.trim(),
      );

      // حفظ الطلب في Firebase
      bool success = false;
      try {
        // تحويل الطلب إلى Map قبل الإرسال
        final orderData = order.toJson();
        success = await FirebaseWebService.addOrder(orderData);
      } catch (e) {
        if (kDebugMode) {
          print('خطأ في حفظ الطلب في Firebase: $e');
        }
        // محاولة الحفظ محلياً كبديل
        await ApiService.addOrder(order);
        success = true;
      }

      if (success && mounted) {
        // عرض رسالة نجاح
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إرسال طلبك بنجاح! سنتواصل معك قريباً'),
            backgroundColor: AppColors.successColor,
          ),
        );

        // العودة للصفحة الرئيسية (MainPage)
        Navigator.of(context).pushNamedAndRemoveUntil('/', (route) => false);
      } else {
        throw Exception('فشل في حفظ الطلب');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء إرسال الطلب: $e'),
            backgroundColor: AppColors.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
